#!/usr/bin/env python3
"""
Тест исправления репозитория микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import MicrotopicRepository, SubjectRepository


async def test_repo():
    """Тестируем репозиторий"""
    print("🧪 Тестируем репозиторий микротем...")
    
    # Получаем предмет
    subjects = await SubjectRepository.get_all()
    test_subject = subjects[0]
    print(f"📚 Тестируем с предметом: {test_subject.name}")
    
    # Тестируем создание одной микротемы
    try:
        print("🧪 Создаем одну микротему...")
        microtopic = await MicrotopicRepository.create("Тест одной микротемы", test_subject.id)
        print(f"✅ Создана: {microtopic.number}. {microtopic.name}")
        
        # Удаляем
        await MicrotopicRepository.delete(microtopic.id)
        print("🗑️ Удалена")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
    
    # Тестируем массовое создание
    try:
        print("\n🧪 Создаем несколько микротем...")
        names = ["Тест 1", "Тест 2", "Тест 3"]
        microtopics = await MicrotopicRepository.create_multiple(names, test_subject.id)
        print(f"✅ Создано: {len(microtopics)}")
        
        for mt in microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Удаляем
        for mt in microtopics:
            await MicrotopicRepository.delete(mt.id)
        print("🗑️ Удалены")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_repo())
