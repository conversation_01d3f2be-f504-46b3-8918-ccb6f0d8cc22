#!/usr/bin/env python3
"""
Скрипт для очистки некорректных записей микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database import get_db_session
from database.models import Microtopic
from sqlalchemy import text, select, delete


async def cleanup_microtopics():
    """Очистка некорректных записей микротем"""
    print("🧹 Очистка некорректных записей микротем...")
    
    async with get_db_session() as session:
        try:
            # Удаляем записи с NULL в поле number
            result = await session.execute(
                delete(Microtopic).where(Microtopic.number.is_(None))
            )
            deleted_count = result.rowcount
            await session.commit()
            
            if deleted_count > 0:
                print(f"🗑️ Удалено некорректных записей: {deleted_count}")
            else:
                print("✅ Некорректных записей не найдено")
            
            # Показываем статистику
            total_result = await session.execute(text("SELECT COUNT(*) FROM microtopics"))
            total_count = total_result.scalar()
            print(f"📊 Всего микротем в базе: {total_count}")
            
        except Exception as e:
            print(f"❌ Ошибка при очистке: {e}")
            await session.rollback()
            raise


async def main():
    """Главная функция"""
    try:
        await cleanup_microtopics()
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
