#!/usr/bin/env python3
"""
Скрипт миграции микротем для добавления поля number и автоматической нумерации
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database import get_db_session
from database.models import Microtopic, Subject
from sqlalchemy import text, select
from sqlalchemy.exc import IntegrityError


async def migrate_microtopics():
    """Миграция микротем: добавление поля number и автоматическая нумерация"""
    print("🔄 Начинаем миграцию микротем...")
    
    async with get_db_session() as session:
        try:
            # 1. Проверяем, есть ли уже поле number
            try:
                await session.execute(text("SELECT number FROM microtopics LIMIT 1"))
                print("✅ Поле 'number' уже существует в таблице microtopics")
            except Exception:
                # 2. Добавляем поле number
                print("📝 Добавляем поле 'number' в таблицу microtopics...")
                await session.execute(text("ALTER TABLE microtopics ADD COLUMN number INTEGER"))
                await session.commit()
                print("✅ Поле 'number' добавлено")

            # 3. Получаем все предметы
            subjects_result = await session.execute(select(Subject))
            subjects = subjects_result.scalars().all()
            
            print(f"📊 Найдено предметов: {len(subjects)}")

            # 4. Для каждого предмета нумеруем микротемы
            for subject in subjects:
                print(f"🔢 Обрабатываем предмет: {subject.name}")
                
                # Получаем микротемы предмета, отсортированные по ID (порядок создания)
                microtopics_result = await session.execute(
                    select(Microtopic)
                    .where(Microtopic.subject_id == subject.id)
                    .order_by(Microtopic.id)
                )
                microtopics = microtopics_result.scalars().all()
                
                # Присваиваем номера
                for i, microtopic in enumerate(microtopics, 1):
                    if microtopic.number is None:  # Только если номер еще не присвоен
                        microtopic.number = i
                        print(f"  📌 Микротема '{microtopic.name}' → номер {i}")
                
                await session.commit()
                print(f"✅ Обработано микротем для предмета '{subject.name}': {len(microtopics)}")

            # 5. Удаляем старое ограничение уникальности (если существует)
            try:
                await session.execute(text("ALTER TABLE microtopics DROP CONSTRAINT unique_microtopic_per_subject"))
                print("🗑️ Удалено старое ограничение уникальности")
            except Exception:
                print("ℹ️ Старое ограничение уникальности не найдено")

            # 6. Добавляем новое ограничение уникальности по номеру
            try:
                await session.execute(text(
                    "ALTER TABLE microtopics ADD CONSTRAINT unique_microtopic_number_per_subject "
                    "UNIQUE (number, subject_id)"
                ))
                await session.commit()
                print("✅ Добавлено новое ограничение уникальности по номеру")
            except IntegrityError:
                print("ℹ️ Ограничение уникальности уже существует")

            # 7. Делаем поле number обязательным
            try:
                await session.execute(text("ALTER TABLE microtopics ALTER COLUMN number SET NOT NULL"))
                await session.commit()
                print("✅ Поле 'number' сделано обязательным")
            except Exception as e:
                print(f"⚠️ Не удалось сделать поле 'number' обязательным: {e}")

            print("🎉 Миграция микротем завершена успешно!")
            
            # 8. Показываем статистику
            total_result = await session.execute(text("SELECT COUNT(*) FROM microtopics"))
            total_count = total_result.scalar()
            print(f"📊 Всего микротем в базе: {total_count}")
            
        except Exception as e:
            print(f"❌ Ошибка при миграции: {e}")
            await session.rollback()
            raise


async def main():
    """Главная функция"""
    try:
        await migrate_microtopics()
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
