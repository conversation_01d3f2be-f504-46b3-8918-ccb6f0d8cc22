#!/usr/bin/env python3
"""
Тест исправленной функциональности микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import MicrotopicRepository, SubjectRepository


async def test_fixed_functionality():
    """Тестируем исправленную функциональность"""
    print("🧪 Тестируем исправленную функциональность микротем...")
    
    # Получаем предмет для тестирования
    subjects = await SubjectRepository.get_all()
    test_subject = subjects[0]
    print(f"📚 Тестируем с предметом: {test_subject.name}")
    
    # Тестируем создание одной микротемы
    print("\n🧪 Тестируем создание одной микротемы...")
    try:
        new_microtopic = await MicrotopicRepository.create("Тестовая микротема", test_subject.id)
        print(f"✅ Создана микротема: {new_microtopic.number}. {new_microtopic.name}")
        
        # Удаляем тестовую микротему
        await MicrotopicRepository.delete(new_microtopic.id)
        print("🗑️ Тестовая микротема удалена")
    except Exception as e:
        print(f"❌ Ошибка при создании микротемы: {e}")
        import traceback
        traceback.print_exc()
    
    # Тестируем массовое создание
    print("\n🧪 Тестируем массовое создание микротем...")
    test_names = [
        "Тестовая микротема 1",
        "Тестовая микротема 2", 
        "Тестовая микротема 3"
    ]
    
    try:
        created_microtopics = await MicrotopicRepository.create_multiple(test_names, test_subject.id)
        print(f"✅ Создано микротем: {len(created_microtopics)}")
        
        for mt in created_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Удаляем тестовые микротемы
        for mt in created_microtopics:
            await MicrotopicRepository.delete(mt.id)
        print("🗑️ Тестовые микротемы удалены")
        
    except Exception as e:
        print(f"❌ Ошибка при массовом создании микротем: {e}")
        import traceback
        traceback.print_exc()
    
    print("🎉 Тестирование завершено!")


async def main():
    """Главная функция"""
    try:
        await test_fixed_functionality()
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
