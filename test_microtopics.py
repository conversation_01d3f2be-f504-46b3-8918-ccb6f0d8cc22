#!/usr/bin/env python3
"""
Тест новой функциональности микротем
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import MicrotopicRepository, SubjectRepository


async def test_microtopics():
    """Тестируем новую функциональность микротем"""
    print("🧪 Тестируем новую функциональность микротем...")
    
    # 1. Получаем предмет для тестирования
    subjects = await SubjectRepository.get_all()
    if not subjects:
        print("❌ Нет предметов для тестирования")
        return
    
    test_subject = subjects[0]  # Берем первый предмет
    print(f"📚 Тестируем с предметом: {test_subject.name}")
    
    # 2. Проверяем существующие микротемы
    existing_microtopics = await MicrotopicRepository.get_by_subject(test_subject.id)
    print(f"📊 Существующих микротем: {len(existing_microtopics)}")
    
    for mt in existing_microtopics[:5]:  # Показываем первые 5
        print(f"  {mt.number}. {mt.name}")
    
    if len(existing_microtopics) > 5:
        print(f"  ... и еще {len(existing_microtopics) - 5} микротем")
    
    # 3. Тестируем получение следующего номера
    next_number = await MicrotopicRepository.get_next_number_for_subject(test_subject.id)
    print(f"🔢 Следующий номер для новой микротемы: {next_number}")
    
    # 4. Тестируем поиск по номеру
    if existing_microtopics:
        first_microtopic = existing_microtopics[0]
        found_microtopic = await MicrotopicRepository.get_by_number(test_subject.id, first_microtopic.number)
        if found_microtopic:
            print(f"✅ Поиск по номеру работает: {found_microtopic.number}. {found_microtopic.name}")
        else:
            print("❌ Поиск по номеру не работает")
    
    # 5. Тестируем создание одной микротемы
    print("\n🧪 Тестируем создание одной микротемы...")
    try:
        new_microtopic = await MicrotopicRepository.create(f"Тестовая микротема {next_number}", test_subject.id)
        print(f"✅ Создана микротема: {new_microtopic.number}. {new_microtopic.name}")
        
        # Удаляем тестовую микротему
        await MicrotopicRepository.delete(new_microtopic.id)
        print("🗑️ Тестовая микротема удалена")
    except Exception as e:
        print(f"❌ Ошибка при создании микротемы: {e}")
    
    # 6. Тестируем массовое создание микротем
    print("\n🧪 Тестируем массовое создание микротем...")
    test_names = [
        "Тестовая микротема 1",
        "Тестовая микротема 2", 
        "Тестовая микротема 3",
        "",  # Пустая строка должна быть пропущена
        "Тестовая микротема 4"
    ]
    
    try:
        created_microtopics = await MicrotopicRepository.create_multiple(test_names, test_subject.id)
        print(f"✅ Создано микротем: {len(created_microtopics)}")
        
        for mt in created_microtopics:
            print(f"  {mt.number}. {mt.name}")
        
        # Удаляем тестовые микротемы
        for mt in created_microtopics:
            await MicrotopicRepository.delete(mt.id)
        print("🗑️ Тестовые микротемы удалены")
        
    except Exception as e:
        print(f"❌ Ошибка при массовом создании микротем: {e}")
    
    # 7. Проверяем финальное состояние
    final_microtopics = await MicrotopicRepository.get_by_subject(test_subject.id)
    print(f"\n📊 Финальное количество микротем: {len(final_microtopics)}")
    
    print("🎉 Тестирование завершено!")


async def test_telegram_message_limits():
    """Тестируем лимиты сообщений Telegram"""
    print("\n🧪 Тестируем лимиты сообщений Telegram...")
    
    # Создаем тестовое сообщение с множеством строк
    lines = []
    for i in range(1, 201):  # 200 строк
        lines.append(f"Микротема {i}")
    
    test_message = "\n".join(lines)
    print(f"📏 Длина тестового сообщения: {len(test_message)} символов")
    print(f"📝 Количество строк: {len(lines)}")
    
    if len(test_message) <= 4096:
        print("✅ Сообщение помещается в лимит Telegram (4096 символов)")
    else:
        print("❌ Сообщение превышает лимит Telegram")
        
        # Найдем максимальное количество строк
        max_lines = 0
        current_length = 0
        
        for i, line in enumerate(lines):
            if current_length + len(line) + 1 <= 4096:  # +1 для \n
                current_length += len(line) + 1
                max_lines = i + 1
            else:
                break
        
        print(f"📊 Максимальное количество строк в лимите: {max_lines}")


async def main():
    """Главная функция"""
    try:
        await test_microtopics()
        await test_telegram_message_limits()
    except Exception as e:
        print(f"💥 Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
